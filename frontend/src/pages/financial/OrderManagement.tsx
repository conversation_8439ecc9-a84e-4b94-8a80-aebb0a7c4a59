import React, {useEffect, useMemo, useState} from 'react'
import {useSearchParams} from 'react-router-dom'
import {FileSpreadsheet, Filter, Plus, RefreshCw, Search} from 'lucide-react'
import {But<PERSON>} from '@/components/ui/Button'
import {Input} from '@/components/ui/Input'
import {Card} from '@/components/ui/Card'

import {DataPagination} from '@/components/common/DataPagination'
import {PermissionButton} from '@/components/auth/PermissionButton'
import {OrderService} from '@/services/financial'
import {OrderCreateDialog} from './components/OrderCreateDialog'
import {SettlementConfirmDialog} from './components/SettlementConfirmDialog'
import {OrderFilterDialog} from './components/OrderFilterDialog'
import {OrderDetailDialog} from './components/OrderDetailDialog'
import {ExpandableTable} from '@/components/ui/expandable-table'
import type {ActualSettlementItem, OrderQueryRequest, OrderVO} from '@/types/financial'
import type {PageResult} from '@/types/api'
import {cn, confirm, error as showError, success, warning} from '@/utils'
import {FINANCIAL_RESPONSIVE_CONFIG} from '@/constants/responsive'
import {useBreakpoint} from '@/hooks/useBreakpoint'

/**
 * 佣金结算订单管理页面
 */
export const OrderManagement: React.FC = () => {
    // URL参数处理
    const [searchParams, setSearchParams] = useSearchParams()

    // 响应式断点检测
    const {isMobile, isTablet} = useBreakpoint()

    // 状态管理
    const [orders, setOrders] = useState<OrderVO[]>([])
    const [loading, setLoading] = useState(false)
    const [pagination, setPagination] = useState({
        current: 1,
        size: 20,  // 只查询主订单，使用合理的分页大小
        total: 0,
        pages: 0
    })

    // 查询条件
    const [queryParams, setQueryParams] = useState<OrderQueryRequest>({
        pageNum: 1,
        pageSize: 20  // 只查询主订单，可以使用合理的分页大小
    })

    // 对话框状态
    const [createDialogOpen, setCreateDialogOpen] = useState(false)
    const [settlementDialogOpen, setSettlementDialogOpen] = useState(false)
    const [filterDialogOpen, setFilterDialogOpen] = useState(false)
    const [detailDialogOpen, setDetailDialogOpen] = useState(false)
    const [selectedOrder, setSelectedOrder] = useState<OrderVO | null>(null)

    const [selectedOrderIds, setSelectedOrderIds] = useState<string[]>([])
    const [ordersToSettle, setOrdersToSettle] = useState<OrderVO[]>([])

    // 搜索关键词
    const [searchKeyword, setSearchKeyword] = useState('')

    /**
     * 加载订单列表
     */
    const loadOrders = async () => {
        try {
            setLoading(true)
            console.log('🔍 开始加载订单列表，查询参数:', queryParams)
            const result: PageResult<OrderVO> = await OrderService.pageOrders(queryParams)
            console.log('✅ 订单列表加载成功:', result)

            // 添加数据验证
            if (result && typeof result === 'object') {
                setOrders(result.records || [])
                setPagination({
                    current: result.current || 1,
                    size: result.size || 10,
                    total: result.total || 0,
                    pages: result.pages || 0
                })
            } else {
                console.error('❌ 订单列表数据格式错误:', result)
                setOrders([])
                setPagination({
                    current: 1,
                    size: 10,
                    total: 0,
                    pages: 0
                })
            }
        } catch (error) {
            console.error('❌ 加载订单列表失败:', error)
            setOrders([])
            setPagination({
                current: 1,
                size: 10,
                total: 0,
                pages: 0
            })
        } finally {
            setLoading(false)
        }
    }

    /**
     * 处理分页变化
     */
    const handlePageChange = (page: number, pageSize: number) => {
        setQueryParams(prev => ({
            ...prev,
            pageNum: page,
            pageSize
        }))
    }

    /**
     * 处理搜索
     */
    const handleSearch = () => {
        setQueryParams(prev => ({
            ...prev,
            pageNum: 1,
            payid: searchKeyword.trim() || undefined
        }))
    }

    /**
     * 处理筛选
     */
    const handleFilter = (filters: Partial<OrderQueryRequest>) => {
        setQueryParams(prev => ({
            ...prev,
            pageNum: 1,
            ...filters
        }))
        setFilterDialogOpen(false)
    }

    /**
     * 重置筛选条件
     */
    const handleResetFilter = () => {
        setQueryParams({
            pageNum: 1,
            pageSize: queryParams.pageSize
        })
        setSearchKeyword('')
    }
    /**
     * 批量结算 - 打开结算确认对话框
     */
    const handleBatchSettle = async () => {
        if (selectedOrderIds.length === 0) {
            warning('请选择要结算的订单')
            return
        }

        // 获取选中的订单详情，只处理主订单
        const selectedMainOrders = orders.filter(order =>
            selectedOrderIds.includes(order.payid) && order.isMainOrder
        )

        if (selectedMainOrders.length === 0) {
            warning('请选择主订单进行结算，子订单将自动跟随主订单状态')
            return
        }

        // 过滤掉已经结算的订单
        const unsettledOrders = selectedMainOrders.filter(order =>
            order.settlementStatus !== 2 // 2 = SettlementStatus.SUCCESS
        )
        const settledOrders = selectedMainOrders.filter(order =>
            order.settlementStatus === 2
        )

        // 如果有已结算的订单，给出提示
        if (settledOrders.length > 0) {
            const settledOrdersInfo = settledOrders.map(order =>
                `${order.agentNickname || order.anchorNickname || '未知用户'} (${order.payid})`
            ).join('、')

            if (unsettledOrders.length === 0) {
                warning(`所选订单均已结算，无需重复结算：${settledOrdersInfo}`)
                return
            } else {
                warning(`以下订单已结算，将被跳过：${settledOrdersInfo}`)
            }
        }

        if (unsettledOrders.length === 0) {
            warning('没有可结算的订单')
            return
        }

        // 只传递未结算的主订单
        setOrdersToSettle(unsettledOrders)
        setSettlementDialogOpen(true)
    }

    /**
     * 确认结算 - 处理实际结算金额
     */
    const handleConfirmSettlement = async (items: ActualSettlementItem[]) => {
        try {
            setLoading(true)
            const settledCount = await OrderService.settleOrdersWithActualAmount(items)
            success(`成功结算 ${settledCount} 个订单`)
            setSelectedOrderIds([])
            setSettlementDialogOpen(false)
            setOrdersToSettle([])
            await loadOrders()
        } catch (error) {
            console.error('批量结算失败:', error)
            showError('批量结算失败')
        } finally {
            setLoading(false)
        }
    }

    /**
     * 批量取消
     */
    const handleBatchCancel = async () => {
        if (selectedOrderIds.length === 0) {
            warning('请选择要取消的订单')
            return
        }

        const confirmed = await confirm(
            '确定要取消选中的订单吗？此操作不可撤销。',
            '确认取消订单',
            {variant: 'destructive', confirmText: '取消订单'}
        )

        if (!confirmed) {
            return
        }

        try {
            setLoading(true)
            const cancelledCount = await OrderService.cancelOrders(selectedOrderIds)
            success(`成功取消 ${cancelledCount} 个订单`)
            setSelectedOrderIds([])
            await loadOrders()
        } catch (error) {
            console.error('批量取消失败:', error)
            showError('批量取消失败')
        } finally {
            setLoading(false)
        }
    }

    /**
     * 导出数据为Excel
     */
    const handleExportExcel = async () => {
        try {
            setLoading(true)

            // 验证是否有选中的数据可导出
            if (selectedOrderIds.length === 0) {
                warning('请先选择要导出的订单数据')
                return
            }

            // 构建导出请求，只包含选中的订单ID
            const exportParams = {
                ...queryParams,
                selectedOrderIds: selectedOrderIds // 添加选中的订单ID列表
            }

            const blob = await OrderService.exportOrdersToExcel(exportParams)

            // 详细验证blob信息
            console.log('📁 收到的blob信息:', {
                size: blob.size,
                type: blob.type,
                constructor: blob.constructor.name
            })

            // 验证blob是否有效
            if (!blob || blob.size === 0) {
                throw new Error('服务器返回的文件为空')
            }

            // 验证blob类型
            if (blob.type && !blob.type.includes('spreadsheet') && !blob.type.includes('excel') && !blob.type.includes('octet-stream')) {
                console.warn('⚠️ Blob类型可能不正确:', blob.type)
                // 尝试读取blob内容的前100个字符来调试
                const text = await blob.slice(0, 100).text()
                console.log('📄 Blob内容预览:', text)
            }

            // 使用更规范的下载方式
            const fileName = `代理结算_${new Date().toISOString().slice(0, 10)}.xlsx`
            downloadFile(blob, fileName)

            // 显示成功提示
            success(`Excel文件导出成功！文件名：${fileName}，文件大小：${(blob.size / 1024).toFixed(2)} KB`)
        } catch (error: any) {
            console.error('导出Excel失败:', error)

            // 显示具体的错误信息
            const errorMessage = error.message || '导出Excel失败，请重试'
            showError(`导出失败：${errorMessage}`)
        } finally {
            setLoading(false)
        }
    }

    /**
     * 规范的文件下载方法
     */
    const downloadFile = (blob: Blob, fileName: string) => {
        try {
            // 创建下载URL
            const url = URL.createObjectURL(blob)

            // 创建临时下载链接
            const downloadLink = document.createElement('a')
            downloadLink.href = url
            downloadLink.download = fileName
            downloadLink.style.display = 'none'

            // 添加到DOM，触发下载，然后清理
            document.body.appendChild(downloadLink)
            downloadLink.click()
            document.body.removeChild(downloadLink)

            // 清理URL对象
            URL.revokeObjectURL(url)
        } catch (error) {
            console.error('文件下载失败:', error)
            throw new Error('文件下载失败')
        }
    }

    // 父子关系映射状态
    const [subOrdersMap, setSubOrdersMap] = useState<Map<string, OrderVO[]>>(new Map())

    /**
     * 构建父子关系映射
     */
    const parentChildMap = useMemo(() => {
        const map = new Map<string, string[]>()
        const childParentMap = new Map<string, string>()

        // 从主订单列表构建映射
        orders.forEach(order => {
            if (order.isMainOrder) {
                map.set(order.payid, [])
            }
        })

        // 从已加载的子订单构建映射
        subOrdersMap.forEach((subOrders, parentId) => {
            const childIds = subOrders.map(sub => sub.payid)
            map.set(parentId, childIds)

            subOrders.forEach(subOrder => {
                childParentMap.set(subOrder.payid, parentId)
            })
        })

        return {parentToChildren: map, childToParent: childParentMap}
    }, [orders, subOrdersMap])

    /**
     * 获取所有子订单ID
     */
    const getAllChildIds = (parentId: string): string[] => {
        return parentChildMap.parentToChildren.get(parentId) || []
    }

    /**
     * 获取父订单ID
     */
    const getParentId = (childId: string): string | undefined => {
        return parentChildMap.childToParent.get(childId)
    }

    /**
     * 计算订单的选中状态
     */
    const getOrderSelectionState = (orderId: string) => {
        const isSelected = selectedOrderIds.includes(orderId)
        const childIds = getAllChildIds(orderId)

        if (childIds.length === 0) {
            // 没有子订单，直接返回选中状态
            return {selected: isSelected, indeterminate: false}
        }

        const selectedChildCount = childIds.filter(childId => selectedOrderIds.includes(childId)).length

        if (selectedChildCount === 0) {
            return {selected: false, indeterminate: false}
        } else if (selectedChildCount === childIds.length) {
            return {selected: true, indeterminate: false}
        } else {
            return {selected: false, indeterminate: true}
        }
    }

    /**
     * 处理复选框变化（支持父子联动）
     */
    const handleCheckboxChange = (payid: string, checked: boolean) => {
        const childIds = getAllChildIds(payid)
        const parentId = getParentId(payid)

        if (childIds.length > 0) {
            // 这是一个父订单
            if (checked) {
                // 选中父订单和所有子订单
                setSelectedOrderIds(prev => {
                    const newIds = new Set([...prev, payid, ...childIds])
                    return Array.from(newIds)
                })
            } else {
                // 取消选中父订单和所有子订单
                setSelectedOrderIds(prev =>
                    prev.filter(id => id !== payid && !childIds.includes(id))
                )
            }
        } else {
            // 这是一个子订单或独立订单
            if (checked) {
                setSelectedOrderIds(prev => {
                    const newIds = [...prev, payid]

                    // 检查是否需要自动选中父订单
                    if (parentId) {
                        const siblingIds = getAllChildIds(parentId)
                        const selectedSiblings = siblingIds.filter(id => newIds.includes(id))

                        if (selectedSiblings.length === siblingIds.length) {
                            // 所有兄弟都被选中，自动选中父订单
                            newIds.push(parentId)
                        }
                    }

                    return Array.from(new Set(newIds))
                })
            } else {
                setSelectedOrderIds(prev => {
                    let newIds = prev.filter(id => id !== payid)

                    // 如果有父订单，自动取消选中父订单
                    if (parentId) {
                        newIds = newIds.filter(id => id !== parentId)
                    }

                    return newIds
                })
            }
        }
    }

    /**
     * 全选/取消全选（包括所有子订单）
     */
    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            const allIds = new Set<string>()

            // 添加所有主订单
            orders.forEach(order => {
                allIds.add(order.payid)

                // 添加所有已加载的子订单
                const childIds = getAllChildIds(order.payid)
                childIds.forEach(childId => allIds.add(childId))
            })

            setSelectedOrderIds(Array.from(allIds))
        } else {
            setSelectedOrderIds([])
        }
    }

    /**
     * 查看订单详情
     */
    const handleViewDetail = (order: OrderVO) => {
        setSelectedOrder(order)
        setDetailDialogOpen(true)
    }

    /**
     * 获取状态徽章样式
     */
    const getStatusBadgeVariant = (status: number) => {
        switch (status) {
            case 0:
                return 'secondary' // 已创建
            case 1:
                return 'default'   // 已计算
            case 2:
                return 'warning'   // 待结算
            case 3:
                return 'success'   // 已结算
            case 4:
                return 'destructive' // 已取消
            default:
                return 'secondary'
        }
    }

    // 检查URL参数，自动打开创建对话框
    useEffect(() => {
        const action = searchParams.get('action')
        if (action === 'create') {
            setCreateDialogOpen(true)
            // 清除URL参数，避免重复触发
            setSearchParams({})
        }
    }, [searchParams, setSearchParams])

    // 初始加载和查询参数变化时重新加载
    useEffect(() => {
        loadOrders()
    }, [queryParams])

    return (
        <div className={cn(
            FINANCIAL_RESPONSIVE_CONFIG.pageLayout.sectionSpacing,
            FINANCIAL_RESPONSIVE_CONFIG.pageLayout.containerPadding
        )}>
            {/* 页面标题 */}
            <div className={cn(
                "flex items-start justify-between",
                FINANCIAL_RESPONSIVE_CONFIG.pageLayout.controlsLayout
            )}>
                <div className="flex-1 min-w-0">
                    <h1 className={cn(
                        "font-bold text-gray-900",
                        FINANCIAL_RESPONSIVE_CONFIG.pageLayout.titleSize
                    )}>
                        {isMobile ? "订单管理" : "佣金结算订单管理"}
                    </h1>
                    <p className={cn(
                        "text-gray-600 mt-1",
                        FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                    )}>
                        {isMobile ? "管理佣金结算" : "管理代理和主播的佣金结算订单"}
                    </p>
                </div>

                <PermissionButton
                    permissions={["commission:order:create"]}
                    onClick={() => setCreateDialogOpen(true)}
                    className={cn(
                        "flex items-center",
                        isMobile ? "gap-1" : "gap-2"
                    )}
                    size={isMobile ? "sm" : "default"}
                >
                    <Plus className={cn(
                        isMobile ? "w-3 h-3" : "w-4 h-4"
                    )}/>
                    {isMobile ? "创建" : "创建订单"}
                </PermissionButton>
            </div>

            {/* 工具栏 */}
            <Card className={cn(
                isMobile ? "p-3" : "p-4"
            )}>
                <div className={cn(
                    "flex gap-3",
                    isMobile ? "flex-col space-y-3" : "items-center justify-between gap-4"
                )}>
                    <div className={cn(
                        "flex gap-2 flex-1",
                        isMobile ? "flex-col space-y-2" : "items-center gap-4"
                    )}>
                        {/* 搜索框 */}
                        <div className={cn(
                            "flex items-center",
                            isMobile ? "gap-1" : "gap-2"
                        )}>
                            <Input
                                placeholder={isMobile ? "搜索订单..." : "搜索订单ID..."}
                                value={searchKeyword}
                                onChange={(e) => setSearchKeyword(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                className={cn(
                                    isMobile ? "flex-1" : "w-64"
                                )}
                            />
                            <Button
                                onClick={handleSearch}
                                variant="outline"
                                size="sm"
                                className={cn(
                                    isMobile && "px-2"
                                )}
                            >
                                <Search className={cn(
                                    isMobile ? "w-3 h-3" : "w-4 h-4"
                                )}/>
                            </Button>
                        </div>

                        {/* 筛选和重置按钮 */}
                        <div className={cn(
                            "flex",
                            isMobile ? "gap-2" : "gap-4"
                        )}>
                            {/* 筛选按钮 */}
                            <Button
                                onClick={() => setFilterDialogOpen(true)}
                                variant="outline"
                                size="sm"
                                className={cn(
                                    "flex items-center",
                                    isMobile ? "gap-1" : "gap-2"
                                )}
                            >
                                <Filter className={cn(
                                    isMobile ? "w-3 h-3" : "w-4 h-4"
                                )}/>
                                筛选
                            </Button>

                            {/* 重置按钮 */}
                            <Button
                                onClick={handleResetFilter}
                                variant="outline"
                                size="sm"
                            >
                                重置
                            </Button>
                        </div>

                        <div className="flex items-center gap-2">
                            {/* 批量操作 */}
                            {selectedOrderIds.length > 0 && (
                                <>
                                    <PermissionButton
                                        permissions={["commission:order:settle"]}
                                        onClick={handleBatchSettle}
                                        size="sm"
                                    >
                                        批量结算 ({selectedOrderIds.length})
                                    </PermissionButton>

                                    <PermissionButton
                                        permissions={["commission:order:cancel"]}
                                        onClick={handleBatchCancel}
                                        variant="outline"
                                        size="sm"
                                    >
                                        批量取消
                                    </PermissionButton>
                                </>
                            )}

                            {/* 导出Excel按钮 */}
                            <PermissionButton
                                permissions={["commission:order:export"]}
                                onClick={handleExportExcel}
                                variant="outline"
                                size="sm"
                                disabled={loading || selectedOrderIds.length === 0}
                            >
                                <FileSpreadsheet className="w-4 h-4 mr-2"/>
                                {selectedOrderIds.length > 0
                                    ? `导出选中 (${selectedOrderIds.length})`
                                    : '导出Excel'
                                }
                            </PermissionButton>

                            {/* 刷新按钮 */}
                            <Button onClick={loadOrders} variant="outline" size="sm">
                                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`}/>
                            </Button>
                        </div>
                    </div>
                </div>
            </Card>

            {/* 订单列表 */}
            <Card>
                {loading ? (
                    <div className="px-4 py-8 text-center text-gray-500">
                        <div className="flex items-center justify-center">
                            <RefreshCw className="w-5 h-5 animate-spin mr-2"/>
                            加载中...
                        </div>
                    </div>
                ) : orders.length === 0 ? (
                    <div className="px-4 py-8 text-center text-gray-500">
                        暂无数据
                    </div>
                ) : (
                    <ExpandableTable
                        orders={orders}
                        selectedOrderIds={selectedOrderIds}
                        onCheckboxChange={handleCheckboxChange}
                        onSelectAll={handleSelectAll}
                        getStatusBadgeVariant={getStatusBadgeVariant}
                        onViewDetail={handleViewDetail}
                        subOrdersMap={subOrdersMap}
                        onSubOrdersMapChange={setSubOrdersMap}
                        getOrderSelectionState={getOrderSelectionState}
                    />
                )}


                {/* 分页 */}
                {!loading && orders.length > 0 && (
                    <div className="px-4 py-3 border-t border-gray-200">
                        <DataPagination
                            current={pagination.current}
                            total={pagination.total}
                            pageSize={pagination.size}
                            onPageChange={(page) => handlePageChange(page, pagination.size)}
                            onPageSizeChange={(pageSize) => handlePageChange(pagination.current, pageSize)}
                        />
                    </div>
                )}
            </Card>
            {/* 对话框组件 */}
            <OrderCreateDialog
                open={createDialogOpen}
                onClose={() => setCreateDialogOpen(false)}
                onSuccess={() => {
                    setCreateDialogOpen(false)
                    loadOrders()
                }}
                prefilledUserId={searchParams.get('targetUserId') ? parseInt(searchParams.get('targetUserId')!) : undefined}
                prefilledUserName={searchParams.get('targetUserName') || undefined}
                prefilledUserType={searchParams.get('userType') as 'agent' | 'anchor' || undefined}
            />
            <OrderFilterDialog
                open={filterDialogOpen}
                onClose={() => setFilterDialogOpen(false)}
                onFilter={handleFilter}
                currentFilters={queryParams}
            />

            <OrderDetailDialog
                open={detailDialogOpen}
                onClose={() => setDetailDialogOpen(false)}
                order={selectedOrder}
            />

            <SettlementConfirmDialog
                open={settlementDialogOpen}
                onClose={() => {
                    setSettlementDialogOpen(false)
                    setOrdersToSettle([])
                }}
                onConfirm={handleConfirmSettlement}
                orders={ordersToSettle}
                loading={loading}
            />
        </div>
    )
}

export default OrderManagement
